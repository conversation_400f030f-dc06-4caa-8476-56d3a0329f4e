from datetime import datetime, time, timedelta, timezone
from typing import Optional
from zoneinfo import ZoneInfo

from configuration.context.tenant_context import (
    reset_current_db_name,
    set_current_db_name,
)
from core.messages import CustomMessageCode
from core.permission.sync_permissions import sync_permissions
from core.permission.sync_role_permissions import sync_roles_permissions
from db.db_connection import TenantDatabase
from enums.pricing_enum import PricingStoragePeriod
from fastapi_pagination import Page, Params
from fastapi_pagination.ext.sqlalchemy import paginate
from schemas.requests.pricing import (
    CreateExtraPricingStorageRequest,
    UpdateTenantConfigurationRequest,
)
from schemas.responses.pricing import TenantExtraStorageResponse
from sqlalchemy import String, cast, or_, select
from sqlalchemy.exc import DBAPIError, OperationalError
from sqlalchemy.ext.asyncio import AsyncSession

from gc_dentist_shared.central_models import (
    MasterPlan,
    MasterPricingStorage,
    TenantClinic,
    TenantExtraStorage,
    TenantPlan,
)
from gc_dentist_shared.core.common.redis import RedisCli
from gc_dentist_shared.core.constants import TIMEZONE_DEFAULT, StorageRedis
from gc_dentist_shared.core.decorators.retry import retry_on_failure
from gc_dentist_shared.core.enums.plan_enum import TenantPlanStatus
from gc_dentist_shared.core.exception_handler.custom_exception import CustomValueError
from gc_dentist_shared.core.logger.config import log
from gc_dentist_shared.tenant_models import TenantConfiguration


class PricingService:
    def __init__(self, central_db_session: AsyncSession, redis_cli: RedisCli = None):
        self.central_db_session = central_db_session
        self.redis_cli = redis_cli

    # region Public Methods
    @retry_on_failure(
        exceptions=(
            OperationalError,
            DBAPIError,
        ),
        log_prefix="create_extra_storage",
    )
    async def create_extra_storage(
        self, payload: CreateExtraPricingStorageRequest, tenant_uuid: str
    ):
        log.info(" START create extra storage")

        async with self.central_db_session.begin():
            master_extra_storage = await self._get_pricing_storage_by_id(
                payload.pricing_key_id, self.central_db_session
            )

            tenant = await self._get_tenant_by_uuid(
                tenant_uuid, self.central_db_session
            )

            extra_storage = await self._create_extra_storage(
                tenant, master_extra_storage, self.central_db_session
            )
            log.info("✅ Create pricing extra storage successfully in central database")

            storage_data = UpdateTenantConfigurationRequest(
                extra_storage=master_extra_storage.storage
            )

            # Sync extra storage data to tenant_configuration(TenantDB)
            await self._sync_storage_to_tenant_database(
                tenant.db_name,
                tenant.tenant_uuid,
                storage_data,
            )

        log.info("✅ Create pricing extra storage successfully")

        log.info(" END create extra storage")
        return extra_storage

    async def get_list_pricing_extra_storage(
        self, tenant_uuid: str, params: Params = None, search: str | None = None
    ) -> Page[TenantExtraStorageResponse]:
        query = await self._build_list_tenant_extra_storage(
            tenant_uuid=tenant_uuid, search=search
        )
        return await paginate(
            self.central_db_session, query, params=params, unique=False
        )

    @retry_on_failure(
        exceptions=(
            OperationalError,
            DBAPIError,
        ),
        log_prefix="update_plan_storage",
    )
    async def update_plan_storage(self, plan_key_id: int, tenant_uuid: str):
        log.info("START update plan storage")
        async with self.central_db_session.begin():
            current_tenant_plan = await self._get_current_tenant_plan(
                tenant_uuid, self.central_db_session
            )

            await self._validate_current_plan(current_tenant_plan, plan_key_id)

            master_plan = await self._validate_and_get_plan_by_key_id(
                plan_key_id, self.central_db_session
            )
            tenant = await self._get_tenant_by_uuid(
                tenant_uuid, self.central_db_session
            )

            # Deactivate current plan
            # TODO(TuanTC) This is temporary solution, need to confirm later
            await self._deactive_current_tenant_plan(
                current_tenant_plan, self.central_db_session
            )

            # Activate a new plan
            new_tenant_plan = await self._activate_tenant_plan(
                plan_key_id, tenant_uuid, self.central_db_session
            )

            storage_data = UpdateTenantConfigurationRequest(
                default_storage=master_plan.default_storage,
                plan_key_id=plan_key_id,
            )

            # Sync plan data to tenant_configuration(TenantDB)
            await self._sync_storage_to_tenant_database(
                tenant.db_name,
                tenant.tenant_uuid,
                storage_data,
                is_update_plan=True,
            )

        log.info("✅ Update plan storage successfully")
        log.info(" END update plan storage")
        return new_tenant_plan

    # endregion

    # region Private Methods - Data Retrieval

    async def _get_pricing_storage_by_id(
        self, key_id: int, db_session: AsyncSession
    ) -> MasterPricingStorage:
        query = select(MasterPricingStorage).where(
            MasterPricingStorage.storage_key_id == key_id,
            MasterPricingStorage.is_active.is_(True),
        )
        result = await db_session.execute(query)
        pricing_storage = result.scalar_one_or_none()

        if not pricing_storage:
            raise CustomValueError(
                message=CustomMessageCode.PRICING_STORAGE_NOT_FOUND.title,
                message_code=CustomMessageCode.PRICING_STORAGE_NOT_FOUND.code,
            )

        return pricing_storage

    async def _get_tenant_by_uuid(
        self, tenant_uuid: str, db_session: AsyncSession
    ) -> TenantClinic:
        query = select(TenantClinic).where(TenantClinic.tenant_uuid == tenant_uuid)
        result = await db_session.execute(query)
        tenant = result.scalar_one_or_none()

        if not tenant:
            raise CustomValueError(
                message=CustomMessageCode.TENANT_NOT_FOUND.title,
                message_code=CustomMessageCode.TENANT_NOT_FOUND.code,
            )

        return tenant

    async def _get_current_tenant_plan(
        self, tenant_uuid: str, db_session: AsyncSession
    ):
        query = select(TenantPlan).where(
            TenantPlan.tenant_uuid == tenant_uuid,
            TenantPlan.status == TenantPlanStatus.ACTIVATE.value,
        )
        result = await db_session.execute(query)
        tenant_plan = result.scalar_one_or_none()
        return tenant_plan

    async def _validate_and_get_plan_by_key_id(
        self, plan_key_id: int, db_session: AsyncSession
    ):
        query = select(MasterPlan).where(MasterPlan.plan_key_id == plan_key_id)
        result = await db_session.execute(query)
        plan = result.scalar_one_or_none()
        if not plan:
            raise CustomValueError(
                message=CustomMessageCode.PRICING_PLAN_NOT_FOUND.title,
                message_code=CustomMessageCode.PRICING_PLAN_NOT_FOUND.code,
            )
        return plan

    async def _get_tenant_configuration(self, db_session, tenant_uuid):
        query_get = select(TenantConfiguration).where(
            TenantConfiguration.tenant_uuid == tenant_uuid
        )
        result = await db_session.execute(query_get)
        tenant_config = result.scalar_one_or_none()

        if not tenant_config:
            raise CustomValueError(
                message=CustomMessageCode.PRICING_STORAGE_TENANT_CONFIGRATION_NOT_FOUND.title,
                message_code=CustomMessageCode.PRICING_STORAGE_TENANT_CONFIGRATION_NOT_FOUND.code,
            )

        return tenant_config

    # endregion

    # region Private Methods - Extra Storage Creation Flow

    async def _create_extra_storage(
        self, tenant, master_extra_storage, db_session: AsyncSession
    ):
        expired_at = self._calculate_expiration_date(master_extra_storage.period)

        extra_storage = TenantExtraStorage(
            tenant_uuid=tenant.tenant_uuid,
            storage_key_id=master_extra_storage.storage_key_id,
            expired_at=expired_at,
        )
        db_session.add(extra_storage)
        await db_session.flush()
        await db_session.refresh(extra_storage)
        return extra_storage

    def _calculate_expiration_date(self, period_id, tenant_tz: str = TIMEZONE_DEFAULT):
        # TODO This is temporary solution, need to confirm later
        days = PricingStoragePeriod.get_days_by_period_id(period_id)
        if days is None:
            raise CustomValueError(
                message=CustomMessageCode.PRICING_STORAGE_PERIOD_NOT_FOUND.title,
                message_code=CustomMessageCode.PRICING_STORAGE_PERIOD_NOT_FOUND.code,
            )

        if days == 0:
            return None

        now_utc = datetime.now(timezone.utc)

        tz = ZoneInfo(tenant_tz)
        now_local = now_utc.astimezone(tz)

        start_date_local = now_local.date()

        valid_to_local = datetime.combine(
            start_date_local + timedelta(days=days), time.min, tz
        )

        expired_at_utc_naive = valid_to_local.astimezone(timezone.utc).replace(
            tzinfo=None
        )
        return expired_at_utc_naive

    # endregion

    # region Private Methods - Plan Management Flow

    async def _validate_current_plan(self, current_tenant_plan, plan_key_id):
        if not current_tenant_plan:
            raise CustomValueError(
                message=CustomMessageCode.PRICING_TENANT_PLAN_NOT_FOUND.title,
                message_code=CustomMessageCode.PRICING_TENANT_PLAN_NOT_FOUND.code,
            )
        if current_tenant_plan.plan_key_id == plan_key_id:
            raise CustomValueError(
                message=CustomMessageCode.PRICING_PLAN_NOT_CHANGED.title,
                message_code=CustomMessageCode.PRICING_PLAN_NOT_CHANGED.code,
            )

    async def _deactive_current_tenant_plan(
        self, current_tenant_plan, central_db_session: AsyncSession
    ):
        current_tenant_plan.status = TenantPlanStatus.DEACTIVATE.value
        await central_db_session.flush()
        await central_db_session.refresh(current_tenant_plan)

    async def _activate_tenant_plan(
        self, plan_key_id, tenant_uuid, central_db_session: AsyncSession
    ):
        existing_plan = await self._find_existing_tenant_plan(
            tenant_uuid, plan_key_id, central_db_session
        )

        if existing_plan:
            return await self._reactivate_existing_plan(
                existing_plan, central_db_session
            )
        else:
            return await self._create_new_tenant_plan(
                plan_key_id, tenant_uuid, central_db_session
            )

    async def _find_existing_tenant_plan(
        self, tenant_uuid: str, plan_key_id: int, central_db_session: AsyncSession
    ):
        query = select(TenantPlan).where(
            TenantPlan.tenant_uuid == tenant_uuid,
            TenantPlan.plan_key_id == plan_key_id,
        )
        result = await central_db_session.execute(query)
        return result.scalar_one_or_none()

    async def _reactivate_existing_plan(
        self, tenant_plan: TenantPlan, central_db_session: AsyncSession
    ) -> TenantPlan:
        tenant_plan.status = TenantPlanStatus.ACTIVATE.value
        await central_db_session.flush()
        await central_db_session.refresh(tenant_plan)
        return tenant_plan

    async def _create_new_tenant_plan(
        self, plan_key_id: int, tenant_uuid: str, central_db_session: AsyncSession
    ) -> TenantPlan:
        new_tenant_plan = TenantPlan(
            tenant_uuid=tenant_uuid,
            plan_key_id=plan_key_id,
            status=TenantPlanStatus.ACTIVATE.value,
            start_at=datetime.now(timezone.utc).replace(tzinfo=None),
        )
        central_db_session.add(new_tenant_plan)
        await central_db_session.flush()
        await central_db_session.refresh(new_tenant_plan)
        return new_tenant_plan

    # endregion

    # region Private Methods - Tenant Database Sync Flow

    async def _sync_storage_to_tenant_database(
        self,
        tenant_db_name: str,
        tenant_uuid: str,
        storage_data: UpdateTenantConfigurationRequest,
        is_update_plan: bool = False,
    ):
        token = set_current_db_name(tenant_db_name)
        try:
            log.info(f"START sync extra storage to tenant: {tenant_uuid}")
            tenant_db_session = await TenantDatabase.get_instance_tenant_db()

            async with tenant_db_session.begin():
                tenant_config = await self._update_tenant_configuration(
                    tenant_uuid, storage_data, tenant_db_session
                )

                # Sync permissions to tenant
                if is_update_plan:
                    await sync_permissions(tenant_db_session, tenant_config.plan_key_id)
                    log.info(
                        f"✅ Successfully synced permissions to tenant: {tenant_uuid}"
                    )
                    await sync_roles_permissions(tenant_db_session)
                    log.info(
                        f"✅ Successfully synced roles_permissions to tenant: {tenant_uuid}"
                    )

            # Update storage limit in Redis
            total_storage = self._calculate_total_storage(tenant_config)
            await self._update_storage_limit_in_redis(
                self.redis_cli, tenant_uuid, total_storage
            )
            # TODO(Tuantc) not implemented logic to save permission with redis by role

            log.info(f"✅ Successfully synced extra storage to tenant: {tenant_uuid}")
            log.info(f" END sync extra storage to tenant: {tenant_uuid}")
        except Exception as e:
            log.error(f"❌ Failed to sync extra storage to tenant: {e}")
            raise e
        finally:
            reset_current_db_name(token)

    async def _update_tenant_configuration(
        self,
        tenant_uuid: str,
        storage_data: UpdateTenantConfigurationRequest,
        db_session: AsyncSession,
    ):
        tenant_config = await self._get_tenant_configuration(db_session, tenant_uuid)
        self._apply_storage_updates(tenant_config, storage_data)

        await db_session.flush()
        await db_session.refresh(tenant_config)
        return tenant_config

    def _apply_storage_updates(
        self,
        tenant_config: TenantConfiguration,
        storage_data: UpdateTenantConfigurationRequest,
    ):
        """Apply storage updates to tenant configuration object"""
        if storage_data.extra_storage is not None:
            tenant_config.extra_storage = (
                tenant_config.extra_storage or 0
            ) + storage_data.extra_storage

        if storage_data.default_storage is not None:
            tenant_config.default_storage = storage_data.default_storage

        if storage_data.plan_key_id is not None:
            tenant_config.plan_key_id = storage_data.plan_key_id

    def _calculate_total_storage(self, tenant_config) -> int:
        extra_storage = tenant_config.extra_storage or 0
        default_storage = tenant_config.default_storage or 0
        return extra_storage + default_storage

    async def _update_storage_limit_in_redis(
        self, redis_cli, tenant_uuid: str, total_storage: int
    ):
        prefix = StorageRedis.STORAGE_LIMIT.value % tenant_uuid
        try:
            result = await redis_cli.set(prefix, total_storage)

            if not result:
                log.warning(
                    f"⚠️ Redis SET returned False for tenant {tenant_uuid}, key={prefix}, value={total_storage}"
                )
                return

            log.info(
                f"✅ Redis storage limit updated: tenant={tenant_uuid}, key={prefix}, value={total_storage}GB"
            )
        except Exception as e:
            log.error(
                f"❌ Exception while updating Redis storage limit: tenant={tenant_uuid}, key={prefix}, "
                f"value={total_storage}, error={e}"
            )

    # endregion

    # region Private Methods - Query Building

    async def _build_list_tenant_extra_storage(
        self, tenant_uuid: str, search: Optional[str] = None
    ):
        fields = [
            TenantExtraStorage.id,
            TenantExtraStorage.storage_key_id,
            TenantExtraStorage.expired_at,
            TenantExtraStorage.created_at,
            TenantExtraStorage.updated_at,
            MasterPricingStorage.name,
            MasterPricingStorage.pricing,
            MasterPricingStorage.storage,
        ]

        where_clause = [TenantExtraStorage.tenant_uuid == tenant_uuid]

        if search:
            search_pattern = f"%{search}%"
            where_clause.append(
                or_(
                    MasterPricingStorage.name.ilike(search_pattern),
                    cast(MasterPricingStorage.storage, String).ilike(search_pattern),
                    cast(MasterPricingStorage.pricing, String).ilike(search_pattern),
                )
            )

        query = (
            select(*fields)
            .join(
                MasterPricingStorage,
                TenantExtraStorage.storage_key_id
                == MasterPricingStorage.storage_key_id,
            )
            .where(*where_clause)
            .order_by(TenantExtraStorage.id.desc())
        )
        return query

    # endregion
