from typing import Optional

from core.messages import CustomMessageCode
from pydantic import BaseModel, Field, field_validator

from gc_dentist_shared.core.common.utils import UUIDString
from gc_dentist_shared.core.enums.pricing_enum import TenantPricingStorageKey


class CreateExtraPricingStorageRequest(BaseModel):
    pricing_key_id: int = Field(..., description="Pricing Key ID", gt=0)

    @field_validator("pricing_key_id")
    @classmethod
    def validate_pricing_key_id(cls, v: int) -> int:
        if v not in [
            TenantPricingStorageKey.BASIC.value,
            TenantPricingStorageKey.PREMIUM.value,
            TenantPricingStorageKey.ENTERPRISE.value,
        ]:
            raise ValueError(CustomMessageCode.PRICING_STORAGE_KEY_ID_IS_INVALID.title)
        return v


class UpdatePlanStorageRequest(BaseModel):
    tenant_uuid: UUIDString = Field(..., description="Tenant UUID")


class UpdateTenantConfigurationRequest(BaseModel):
    default_storage: Optional[int] = None
    extra_storage: Optional[int] = None
    plan_key_id: Optional[int] = None
