from datetime import date, timedelta
from unittest.mock import patch
from urllib.parse import parse_qs, urlparse

import pytest
import pytest_asyncio
from core.messages import CustomMessageCode
from fastapi import status
from tests.helpers.cloudfront_mock import mock_cloudfront_config_factory  # noqa: F401
from tests.helpers.insert_data.insert_document_management import (
    unittest_insert_document,
)
from tests.helpers.insert_data.insert_patient import unittest_insert_patient


@pytest.fixture(scope="module")
def _headers(tenant_uuid):
    return {
        "X-Tenant-UUID": tenant_uuid,
    }


@pytest_asyncio.fixture(scope="module")
async def setup_data(async_tenant_db_session_object):
    async with async_tenant_db_session_object.begin():
        patient_user_id = await unittest_insert_patient(async_tenant_db_session_object)

        documents = [
            await unittest_insert_document(
                async_tenant_db_session_object,
                patient_user_id=patient_user_id,
            ),
            await unittest_insert_document(
                async_tenant_db_session_object,
                patient_user_id=patient_user_id,
                custom_document_fields={
                    "examination_date": date.today() - timedelta(days=1)
                },
            ),
            await unittest_insert_document(
                async_tenant_db_session_object,
                patient_user_id=patient_user_id,
                custom_document_fields={
                    "examination_date": date.today() - timedelta(days=2)
                },
            ),
        ]

        return {"patient_user_id": patient_user_id, "documents": documents}


@pytest.mark.asyncio
async def test_get_list_success_empty_data(
    async_client, async_tenant_db_session_object, _headers
):
    async with async_tenant_db_session_object.begin():
        patient_user_id = await unittest_insert_patient(async_tenant_db_session_object)

    response = await async_client.get(
        f"/v1_0/document-managements/patients/{patient_user_id}", headers=_headers
    )

    assert response.status_code == status.HTTP_200_OK
    response_json = response.json()
    assert response_json["success"] is True

    data = response_json["data"]
    assert data["total"] == 0
    assert len(data["items"]) == 0


@pytest.mark.asyncio
async def test_get_list_success_structure(async_client, _headers, setup_data):
    patient_user_id = setup_data.get("patient_user_id")
    documents = setup_data.get("documents")

    response = await async_client.get(
        f"/v1_0/document-managements/patients/{patient_user_id}", headers=_headers
    )

    assert response.status_code == status.HTTP_200_OK
    response_json = response.json()
    assert response_json["success"] is True
    assert response_json["data"] is not None

    data = response_json["data"]
    assert isinstance(data["items"], list)
    assert "total" in data
    assert "page" in data
    assert "size" in data
    assert data["total"] == len(documents)

    first_item = data["items"][0]
    assert "examination_date" in first_item
    assert "list_document" in first_item

    first_document = first_item.get("list_document")[0]
    assert isinstance(first_document["preview_document_data"], dict)
    assert isinstance(first_document["document_data"], dict)


@pytest.mark.asyncio
async def test_get_list_document_valid_signed_url(
    async_client, _headers, setup_data, mock_cloudfront_config_factory  # noqa: F811
):
    patient_user_id = setup_data.get("patient_user_id")

    context = await mock_cloudfront_config_factory()
    with context:
        response = await async_client.get(
            f"/v1_0/document-managements/patients/{patient_user_id}?page=1&size=2",
            headers=_headers,
        )

        assert response.status_code == status.HTTP_200_OK
        data_page = response.json()["data"]

        first_item = data_page["items"][0]
        first_document = first_item["list_document"][0]

        document_data = first_document["document_data"].values()
        preview_document_data = first_document["preview_document_data"].values()
        all_files = list(document_data) + list(preview_document_data)
        for f in all_files:
            parsed_url = urlparse(f)
            query_params = parse_qs(parsed_url.query)
            assert "Signature" in query_params


@pytest.mark.asyncio
@pytest.mark.parametrize(
    "data_index",
    [
        0,
        1,
        2,
    ],
)
async def test_get_list_success_with_filters(
    async_client, _headers, setup_data, data_index
):
    patient_user_id = setup_data.get("patient_user_id")
    documents = setup_data.get("documents")
    document_group_id = documents[data_index]["document_group_id"]
    response = await async_client.get(
        f"/v1_0/document-managements/patients/{patient_user_id}",
        headers=_headers,
        params={"document_group_ids": [document_group_id]},
    )

    assert response.status_code == status.HTTP_200_OK
    response_json = response.json()
    assert response_json["success"] is True
    assert response_json["data"] is not None

    data = response_json["data"]
    items = data["items"]
    assert len(items) == 1


@pytest.mark.asyncio
async def test_get_list_success_pagination(async_client, _headers, setup_data):
    patient_user_id = setup_data.get("patient_user_id")
    documents = setup_data.get("documents")

    # check page 1
    response = await async_client.get(
        f"/v1_0/document-managements/patients/{patient_user_id}?page=1&size=2",
        headers=_headers,
    )

    assert response.status_code == status.HTTP_200_OK
    data_page = response.json()["data"]

    assert data_page["total"] == len(documents)
    assert len(data_page["items"]) == 2
    assert data_page["page"] == 1
    assert data_page["size"] == 2

    assert (
        data_page["items"][0]["list_document"][0]["id"] == documents[0]["document_id"]
    )
    assert (
        data_page["items"][1]["list_document"][0]["id"] == documents[1]["document_id"]
    )

    # check page 2
    response_2 = await async_client.get(
        f"/v1_0/document-managements/patients/{patient_user_id}?page=2&size=2",
        headers=_headers,
    )

    assert response_2.status_code == status.HTTP_200_OK
    data_page_2 = response_2.json()["data"]

    assert data_page_2["total"] == len(documents)
    assert len(data_page_2["items"]) == 1
    assert data_page_2["page"] == 2


@pytest.mark.asyncio
@pytest.mark.parametrize(
    "params",
    [
        {"page": 0, "size": 10},
        {"page": -1, "size": 10},
        {"page": 1, "size": 0},
        {"page": 1, "size": -5},
        {"document_group_ids": "invalid"},
    ],
)
async def test_get_list_invalid_pagination_input(
    async_client, _headers, setup_data, params
):
    patient_user_id = setup_data.get("patient_user_id")
    response = await async_client.get(
        f"/v1_0/document-managements/patients/{patient_user_id}",
        params=params,
        headers=_headers,
    )
    assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY
    error_data = response.json()
    assert "data" in error_data
    assert isinstance(error_data["data"], list)
    assert len(error_data["data"]) > 0


@pytest.mark.asyncio
async def test_get_list_exception_error(async_client, _headers, setup_data):
    """Test general exception handling"""
    # Patch the service to throw a generic Exception
    with patch(
        "services.document_managements.document_management_service.DocumentManagementService.get_list",
        side_effect=Exception("Something went wrong"),
    ):
        patient_user_id = setup_data.get("patient_user_id")
        response = await async_client.get(
            f"/v1_0/document-managements/patients/{patient_user_id}", headers=_headers
        )

    assert response.status_code == status.HTTP_400_BAD_REQUEST
    assert (
        response.json()["messageCode"]
        == CustomMessageCode.DOCUMENT_GET_LIST_FAILED.code
    )


@pytest.mark.asyncio
async def test_cleaned_up(async_tenant_db_session_object, truncate_table):
    await truncate_table(
        async_tenant_db_session_object,
        [
            "patient_profiles",
            "patient_users",
            "document_group",
            "document_managements",
        ],
    )
