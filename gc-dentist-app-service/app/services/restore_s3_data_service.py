from datetime import datetime, timezone

from configuration.context.tenant_context import (
    reset_current_db_name,
    set_current_db_name,
)
from configuration.settings import configuration
from core.constants import TenantClinicStatus
from core.messages import CustomMessageCode
from db.db_connection import TenantDatabase
from fastapi_pagination import Params
from fastapi_pagination.ext.sqlalchemy import paginate
from schemas.requests.restore_s3_data_schema import (
    DeleteExpiredRestoredDataRequest,
    RestoreRecord,
    RestoreS3DocumentRequest,
    SearchRestoreS3DataRequest,
    UpdateRestoredStatusRequest,
)
from sqlalchemy import delete, exists, or_, select
from sqlalchemy.exc import DBAPIError, OperationalError
from sqlalchemy.ext.asyncio import AsyncSession

from gc_dentist_shared.central_models import TenantClinic
from gc_dentist_shared.core.common.cloudfront import CloudFront
from gc_dentist_shared.core.common.s3_bucket import S3Client
from gc_dentist_shared.core.common.utils import is_valid_uuid
from gc_dentist_shared.core.decorators.retry import retry_on_failure
from gc_dentist_shared.core.enums.document import DocumentStatus
from gc_dentist_shared.core.enums.s3_enums import S3RestoreStatus, S3RestoreTier
from gc_dentist_shared.core.exception_handler.custom_exception import CustomValueError
from gc_dentist_shared.core.logger.config import log
from gc_dentist_shared.tenant_models import DocumentManagement, RestoreS3Data


class S3DataRestorationService:
    def __init__(
        self,
        tenant_db_session: AsyncSession = None,
        central_db_session: AsyncSession = None,
        s3_client: S3Client = None,
        cloudfront_client: CloudFront = None,
    ):
        self.tenant_db_session = tenant_db_session
        self.central_db_session = central_db_session
        self.s3_client = s3_client
        self.cloudfront_client = cloudfront_client

    async def restore_document_from_s3(self, request_data: RestoreS3DocumentRequest):
        await self.validate_restoring_document(
            document_uuid=request_data.document_uuid, version_id=request_data.version_id
        )

        document_data = await self.get_document_management_data(
            document_uuid=request_data.document_uuid,
            version_id=request_data.version_id,
        )
        object_keys = document_data["object_keys"]
        version_id = document_data["version_id"]
        for object_key in object_keys:
            await self.process_s3_restoration(
                object_key=object_key,
                version_id=version_id,
                document_uuid=request_data.document_uuid,
            )

    async def update_restored_status(self, request_data: UpdateRestoredStatusRequest):
        grouped_records_by_tenant_db = await self.build_grouped_records_by_tenant_db(
            records=request_data.records
        )
        for tenant_db_name, records in grouped_records_by_tenant_db.items():
            await self.update_restored_status_for_tenant(tenant_db_name, records)

    async def remove_expired_restored_data(
        self, request_data: DeleteExpiredRestoredDataRequest
    ):
        grouped_records_by_tenant_db = await self.build_grouped_records_by_tenant_db(
            records=request_data.records
        )
        for tenant_db_name, records in grouped_records_by_tenant_db.items():
            await self.remove_expired_restored_data_for_tenant(tenant_db_name, records)

    @retry_on_failure(
        exceptions=(OperationalError, DBAPIError),
        log_prefix="get_document_management_data",
    )
    async def get_document_management_data(
        self, document_uuid: str, version_id: int
    ) -> dict:
        async with self.tenant_db_session.begin():
            query = select(
                DocumentManagement.document_data, DocumentManagement.version_id
            ).where(
                DocumentManagement.document_uuid == document_uuid,
                DocumentManagement.version_id == version_id,
                DocumentManagement.status == DocumentStatus.ACTIVATED.value,
            )
            result = await self.tenant_db_session.execute(query)
            document_management = result.one_or_none()
            if not document_management:
                raise CustomValueError(
                    message_code=CustomMessageCode.DOCUMENT_MANAGEMENT_NOT_FOUND.code,
                    message=CustomMessageCode.DOCUMENT_MANAGEMENT_NOT_FOUND.title,
                )
            document_data, version_id = document_management
            return {
                "object_keys": document_data.values(),
                "version_id": version_id,
            }

    @retry_on_failure(
        exceptions=(OperationalError, DBAPIError),
        log_prefix="validate_restoring_document",
    )
    async def validate_restoring_document(self, document_uuid: str, version_id: int):
        async with self.tenant_db_session.begin():
            stmt = exists().where(
                RestoreS3Data.document_uuid == document_uuid,
                RestoreS3Data.version_id == version_id,
                RestoreS3Data.status == S3RestoreStatus.RESTORING.value,
            )
            result = await self.tenant_db_session.execute(select(stmt))
            if result.scalar():
                raise CustomValueError(
                    message_code=CustomMessageCode.DOCUMENT_IS_RESTORING.code,
                    message=CustomMessageCode.DOCUMENT_IS_RESTORING.title,
                )

    @retry_on_failure(
        exceptions=(OperationalError, DBAPIError),
        log_prefix="process_s3_restoration",
    )
    async def process_s3_restoration(
        self, object_key: str, version_id: int, document_uuid: str
    ):
        async with self.tenant_db_session.begin():
            self.tenant_db_session.add(
                RestoreS3Data(
                    document_uuid=document_uuid,
                    version_id=version_id,
                    s3_url=object_key,
                    status=S3RestoreStatus.RESTORING.value,
                    requested_at=datetime.now(timezone.utc),
                )
            )

            await self.s3_client.restore_object(
                key=object_key,
                days=configuration.S3_TEMP_FILE_TTL,
                tier=S3RestoreTier.BULK.value,
            )

    @retry_on_failure(
        exceptions=(OperationalError, DBAPIError),
        log_prefix="build_grouped_records_by_tenant_db",
    )
    async def build_grouped_records_by_tenant_db(
        self, records: list[RestoreRecord]
    ) -> dict[str, list[RestoreRecord]]:
        tenant_uuid_to_records: dict[str, list[RestoreRecord]] = {}
        # 1) Group records by tenant_uuid extracted from the object_key
        for record in records:
            tenant_uuid = self._extract_tenant_uuid(record.object_key)
            if not tenant_uuid:
                log.info(
                    "Skip record without tenant_uuid in object_key: %s",
                    record.object_key,
                )
                continue
            tenant_uuid_to_records.setdefault(tenant_uuid, []).append(record)

        if not tenant_uuid_to_records:
            return {}

        # 2) Fetch db_names for all tenant_uuids
        async with self.central_db_session.begin():
            stmt = select(TenantClinic.tenant_uuid, TenantClinic.db_name).where(
                TenantClinic.tenant_uuid.in_(list(tenant_uuid_to_records.keys())),
                TenantClinic.status == TenantClinicStatus.SUCCESS.value,
            )
            result = await self.central_db_session.execute(stmt)
            uuid_db_rows = result.all()

        uuid_from_db: dict[str, str] = {
            str(uuid): db_name for uuid, db_name in uuid_db_rows if db_name
        }

        # 3) Build grouped_records_by_tenant_db using the mapping
        grouped_records_by_tenant_db: dict[str, list[RestoreRecord]] = {}
        for tenant_uuid, records in tenant_uuid_to_records.items():
            tenant_db = uuid_from_db.get(tenant_uuid)
            if not tenant_db:
                log.info(
                    "Tenant DB not found for tenant_uuid=%s; skipping %d record(s)",
                    tenant_uuid,
                    len(records),
                )
                continue
            grouped_records_by_tenant_db.setdefault(tenant_db, []).extend(records)

        return grouped_records_by_tenant_db

    async def update_restored_status_for_tenant(
        self, tenant_db_name: str, records: list[RestoreRecord]
    ):
        log.info(f"Update restore s3 status for tenant_db: {tenant_db_name}")
        token_db = set_current_db_name(tenant_db_name)
        tenant_db_session = await TenantDatabase.get_instance_tenant_db()
        try:
            for record in records:
                try:
                    await self._process_restore_record_for_tenant(
                        tenant_db_session, record
                    )
                except Exception as e:
                    log.error(
                        "❌ Failed to process restore record for key=%s in tenant_db=%s: %s",
                        record.object_key,
                        tenant_db_name,
                        str(e),
                    )
                    continue

        except Exception as e:
            log.error(
                "❌ Update restore status error for tenant_db=%s: %s",
                tenant_db_name,
                str(e),
            )

        finally:
            reset_current_db_name(token_db)
            await tenant_db_session.close()

    @retry_on_failure(
        exceptions=(OperationalError, DBAPIError),
        log_prefix="_process_restore_record_for_tenant",
    )
    async def _process_restore_record_for_tenant(
        self, tenant_db_session: AsyncSession, record: RestoreRecord
    ) -> None:
        async with tenant_db_session.begin():
            # 1) Load the restoring item
            stmt = select(RestoreS3Data).where(
                RestoreS3Data.s3_url == record.object_key,
                RestoreS3Data.status == S3RestoreStatus.RESTORING.value,
            )
            result = await tenant_db_session.execute(stmt)
            restoring_item = result.scalar_one_or_none()

            if not restoring_item:
                log.info("No restoring item found for key=%s", record.object_key)
                return

            # 2) Generate a temporary URL
            temp_url = self.cloudfront_client.generate_signed_url(
                file_path=record.object_key,
                version=restoring_item.version_id,
                expires=record.expires_at,
                return_full_url=True,
            )

            # 3) Update and save restoration info
            restoring_item.restored_at = datetime.now(timezone.utc)
            restoring_item.status = S3RestoreStatus.RESTORED.value
            restoring_item.s3_url_temp = temp_url
            restoring_item.expires_at = record.expires_at
            tenant_db_session.add(restoring_item)
            await tenant_db_session.flush()

    async def remove_expired_restored_data_for_tenant(
        self, tenant_db_name: str, records: list[RestoreRecord]
    ):
        log.info(f"Delete expired restored s3 data for tenant_db: {tenant_db_name}")
        token_db = set_current_db_name(tenant_db_name)
        tenant_db_session = await TenantDatabase.get_instance_tenant_db()
        try:
            object_keys = [record.object_key for record in records]
            async with tenant_db_session.begin():
                stmt = delete(RestoreS3Data).where(
                    RestoreS3Data.s3_url.in_(object_keys),
                    RestoreS3Data.status.in_(
                        [S3RestoreStatus.RESTORED.value, S3RestoreStatus.EXPIRED.value]
                    ),
                    RestoreS3Data.expires_at < datetime.now(timezone.utc),
                )
                await tenant_db_session.execute(stmt)

        except Exception as e:
            log.error(
                "❌ Delete expired restored data error for tenant_db=%s: %s",
                tenant_db_name,
                str(e),
            )
        finally:
            reset_current_db_name(token_db)
            await tenant_db_session.close()

    @staticmethod
    def _extract_tenant_uuid(object_key: str) -> str | None:
        """
        Expected key format: main/{tenant_uuid}/...
        Returns tenant_uuid if present, otherwise None.
        """
        parts = object_key.split("/")
        if len(parts) >= 2 and is_valid_uuid(parts[1]):
            return parts[1]

        return None

    async def search_restore_document_data(
        self, search_request: SearchRestoreS3DataRequest, params: Params
    ) -> dict:
        query = await self._build_search_document_restore_data(search_request)
        return await paginate(
            self.tenant_db_session, query, params=params, unique=False
        )

    async def _build_search_document_restore_data(
        self, search_request: SearchRestoreS3DataRequest
    ):
        fields = [
            RestoreS3Data.id,
            RestoreS3Data.document_uuid,
            RestoreS3Data.version_id,
            RestoreS3Data.s3_url,
            RestoreS3Data.status,
            RestoreS3Data.requested_at,
            RestoreS3Data.restored_at,
            RestoreS3Data.expires_at,
        ]

        filters = []
        if search_request.document_uuid:
            filters.append(RestoreS3Data.document_uuid == search_request.document_uuid)

        if search_request.status:
            filters.append(RestoreS3Data.status == search_request.status)

        if search_request.search:
            search_pattern = f"%{search_request.search}%"
            filters.append(
                or_(
                    RestoreS3Data.s3_url.ilike(search_pattern)
                    | RestoreS3Data.document_uuid.ilike(search_pattern)
                    | RestoreS3Data.s3_url_temp.ilike(search_pattern)
                )
            )

        query = (
            select(*fields)
            .where(
                *filters,
            )
            .order_by(RestoreS3Data.requested_at.desc())
        )
        return query

    async def get_detail_restore_document_data(self, id: str) -> dict:
        async with self.tenant_db_session.begin():
            query = select(
                RestoreS3Data.id,
                RestoreS3Data.document_uuid,
                RestoreS3Data.version_id,
                RestoreS3Data.s3_url,
                RestoreS3Data.status,
                RestoreS3Data.requested_at,
                RestoreS3Data.restored_at,
                RestoreS3Data.expires_at,
            ).where(
                RestoreS3Data.id == id,
            )

            result = await self.tenant_db_session.execute(query)
            restore_s3_data = result.mappings().one_or_none()

        if not restore_s3_data:
            raise CustomValueError(
                message_code=CustomMessageCode.DOCUMENT_MANAGEMENT_NOT_FOUND.code,
                message=CustomMessageCode.DOCUMENT_MANAGEMENT_NOT_FOUND.title,
            )

        return restore_s3_data
